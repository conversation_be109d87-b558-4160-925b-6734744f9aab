{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n * @type {React.Context<{} | {expanded: boolean, disabled: boolean, toggle: () => void}>}\n */\nconst ImageListContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ImageListContext.displayName = 'ImageListContext';\n}\nexport default ImageListContext;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}