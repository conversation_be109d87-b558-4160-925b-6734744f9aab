{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n * @type {React.Context<{} | {expanded: boolean, disabled: boolean, toggle: () => void}>}\n */\nconst AccordionContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  AccordionContext.displayName = 'AccordionContext';\n}\nexport default AccordionContext;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}