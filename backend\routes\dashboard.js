const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const dashboardController = require('../controllers/dashboardController');

router.get('/', authenticateToken, dashboardController.getDashboardData);
router.get('/balance-history', authenticateToken, dashboardController.getBalanceHistory);
router.get('/expenses-by-category', authenticateToken, dashboardController.getExpensesByCategory);

module.exports = router;