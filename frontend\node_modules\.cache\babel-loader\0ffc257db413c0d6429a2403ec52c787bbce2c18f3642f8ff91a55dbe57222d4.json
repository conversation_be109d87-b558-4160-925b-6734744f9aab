{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useTimeout as useTimeout } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\n/**\n * The basic building block for creating custom snackbar.\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)\n *\n * API:\n *\n * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)\n */\nfunction useSnackbar(parameters = {}) {\n  const {\n    autoHideDuration = null,\n    disableWindowBlurListener = false,\n    onClose,\n    open,\n    resumeHideDuration\n  } = parameters;\n  const timerAutoHide = useTimeout();\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (!nativeEvent.defaultPrevented) {\n        // IE11, Edge (prior to using Blink?) use 'Esc'\n        if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n          // not calling `preventDefault` since we don't know if people may ignore this event e.g. a permanently open snackbar\n          onClose == null || onClose(nativeEvent, 'escapeKeyDown');\n        }\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [open, onClose]);\n  const handleClose = useEventCallback((event, reason) => {\n    onClose == null || onClose(event, reason);\n  });\n  const setAutoHideTimer = useEventCallback(autoHideDurationParam => {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    timerAutoHide.start(autoHideDurationParam, () => {\n      handleClose(null, 'timeout');\n    });\n  });\n  React.useEffect(() => {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return timerAutoHide.clear;\n  }, [open, autoHideDuration, setAutoHideTimer, timerAutoHide]);\n  const handleClickAway = event => {\n    onClose == null || onClose(event, 'clickaway');\n  };\n\n  // Pause the timer when the user is interacting with the Snackbar\n  // or when the user hide the window.\n  const handlePause = timerAutoHide.clear;\n\n  // Restart the timer when the user is no longer interacting with the Snackbar\n  // or when the window is shown back.\n  const handleResume = React.useCallback(() => {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  const createHandleBlur = otherHandlers => event => {\n    const onBlurCallback = otherHandlers.onBlur;\n    onBlurCallback == null || onBlurCallback(event);\n    handleResume();\n  };\n  const createHandleFocus = otherHandlers => event => {\n    const onFocusCallback = otherHandlers.onFocus;\n    onFocusCallback == null || onFocusCallback(event);\n    handlePause();\n  };\n  const createMouseEnter = otherHandlers => event => {\n    const onMouseEnterCallback = otherHandlers.onMouseEnter;\n    onMouseEnterCallback == null || onMouseEnterCallback(event);\n    handlePause();\n  };\n  const createMouseLeave = otherHandlers => event => {\n    const onMouseLeaveCallback = otherHandlers.onMouseLeave;\n    onMouseLeaveCallback == null || onMouseLeaveCallback(event);\n    handleResume();\n  };\n  React.useEffect(() => {\n    // TODO: window global should be refactored here\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return () => {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, open, handleResume, handlePause]);\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));\n    return _extends({\n      // ClickAwayListener adds an `onClick` prop which results in the alert not being announced.\n      // See https://github.com/mui/material-ui/issues/29080\n      role: 'presentation'\n    }, externalProps, externalEventHandlers, {\n      onBlur: createHandleBlur(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onMouseEnter: createMouseEnter(externalEventHandlers),\n      onMouseLeave: createMouseLeave(externalEventHandlers)\n    });\n  };\n  return {\n    getRootProps,\n    onClickAway: handleClickAway\n  };\n}\nexport default useSnackbar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}