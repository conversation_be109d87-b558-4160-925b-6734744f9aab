{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onChange\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport debounce from '../utils/debounce';\nimport { ownerWindow, unstable_useEnhancedEffect as useEnhancedEffect } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  width: 99,\n  height: 99,\n  position: 'absolute',\n  top: -9999,\n  overflow: 'scroll'\n};\n\n/**\n * @ignore - internal component.\n * The component originates from https://github.com/STORIS/react-scrollbar-size.\n * It has been moved into the core in order to minimize the bundle size.\n */\nexport default function ScrollbarSize(props) {\n  const {\n      onChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const scrollbarHeight = React.useRef();\n  const nodeRef = React.useRef(null);\n  const setMeasurements = () => {\n    scrollbarHeight.current = nodeRef.current.offsetHeight - nodeRef.current.clientHeight;\n  };\n  useEnhancedEffect(() => {\n    const handleResize = debounce(() => {\n      const prevHeight = scrollbarHeight.current;\n      setMeasurements();\n      if (prevHeight !== scrollbarHeight.current) {\n        onChange(scrollbarHeight.current);\n      }\n    });\n    const containerWindow = ownerWindow(nodeRef.current);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [onChange]);\n  React.useEffect(() => {\n    setMeasurements();\n    onChange(scrollbarHeight.current);\n  }, [onChange]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    style: styles\n  }, other, {\n    ref: nodeRef\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? ScrollbarSize.propTypes = {\n  onChange: PropTypes.func.isRequired\n} : void 0;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}