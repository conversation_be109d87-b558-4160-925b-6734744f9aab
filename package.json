{"name": "finance-manager", "version": "1.0.0", "description": "Aplicativo de gerenciamento de finanças pessoais", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "test": "cd frontend && npm test && cd ../backend && npm test"}, "devDependencies": {"concurrently": "^8.2.2"}}