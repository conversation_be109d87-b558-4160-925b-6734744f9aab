{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getImageListUtilityClass(slot) {\n  return generateUtilityClass('MuiImageList', slot);\n}\nconst imageListClasses = generateUtilityClasses('MuiImageList', ['root', 'masonry', 'quilted', 'standard', 'woven']);\nexport default imageListClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}