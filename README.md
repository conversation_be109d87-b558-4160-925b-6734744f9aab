# Finance Manager - Gerenciador de Finanças Pessoais

## 🚀 Funcionalidades

- Dashboard com visão geral das finanças
- Gestão completa de transações
- Categorização automática
- Relatórios e gráficos detalhados
- Metas financeiras
- Importação de extratos bancários
- PWA com suporte offline

## 🛠️ Tecnologias

- **Frontend**: React.js, Material-UI, Chart.js
- **Backend**: Node.js, Express, JWT
- **Banco**: PostgreSQL
- **PWA**: Service Workers

## 📦 Instalação

```bash
# Clone o repositório
git clone <repo-url>
cd finance-manager

# Instale dependências
npm install
cd frontend && npm install
cd ../backend && npm install

# Configure o banco de dados
cd backend && npm run setup-db

# Execute o projeto
npm run dev
```

## 🔧 Configuração

1. Configure as variáveis de ambiente no arquivo `.env`
2. Execute as migrações do banco de dados
3. Acesse http://localhost:3000