{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActions', slot);\n}\nconst cardActionsClasses = generateUtilityClasses('MuiCardActions', ['root', 'spacing']);\nexport default cardActionsClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}