{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSpeedDialActionUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDialAction', slot);\n}\nconst speedDialActionClasses = generateUtilityClasses('MuiSpeedDialAction', ['fab', 'fabClosed', 'staticTooltip', 'staticTooltipClosed', 'staticTooltipLabel', 'tooltipPlacementLeft', 'tooltipPlacementRight']);\nexport default speedDialActionClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}