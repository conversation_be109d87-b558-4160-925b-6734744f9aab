{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"className\", \"component\", \"disablePointerEvents\", \"disableTypography\", \"position\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport Typography from '../Typography';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport useFormControl from '../FormControl/useFormControl';\nimport styled from '../styles/styled';\nimport inputAdornmentClasses, { getInputAdornmentUtilityClass } from './inputAdornmentClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, styles[`position${capitalize(ownerState.position)}`], ownerState.disablePointerEvents === true && styles.disablePointerEvents, styles[ownerState.variant]];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePointerEvents,\n    hiddenLabel,\n    position,\n    size,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', disablePointerEvents && 'disablePointerEvents', position && `position${capitalize(position)}`, variant, hiddenLabel && 'hiddenLabel', size && `size${capitalize(size)}`]\n  };\n  return composeClasses(slots, getInputAdornmentUtilityClass, classes);\n};\nconst InputAdornmentRoot = styled('div', {\n  name: 'MuiInputAdornment',\n  slot: 'Root',\n  overridesResolver\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'flex',\n  height: '0.01em',\n  // Fix IE11 flexbox alignment. To remove at some point.\n  maxHeight: '2em',\n  alignItems: 'center',\n  whiteSpace: 'nowrap',\n  color: (theme.vars || theme).palette.action.active\n}, ownerState.variant === 'filled' && {\n  // Styles applied to the root element if `variant=\"filled\"`.\n  [`&.${inputAdornmentClasses.positionStart}&:not(.${inputAdornmentClasses.hiddenLabel})`]: {\n    marginTop: 16\n  }\n}, ownerState.position === 'start' && {\n  // Styles applied to the root element if `position=\"start\"`.\n  marginRight: 8\n}, ownerState.position === 'end' && {\n  // Styles applied to the root element if `position=\"end\"`.\n  marginLeft: 8\n}, ownerState.disablePointerEvents === true && {\n  // Styles applied to the root element if `disablePointerEvents={true}`.\n  pointerEvents: 'none'\n}));\nconst InputAdornment = /*#__PURE__*/React.forwardRef(function InputAdornment(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiInputAdornment'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      disablePointerEvents = false,\n      disableTypography = false,\n      position,\n      variant: variantProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl() || {};\n  let variant = variantProp;\n  if (variantProp && muiFormControl.variant) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (variantProp === muiFormControl.variant) {\n        console.error('MUI: The `InputAdornment` variant infers the variant prop ' + 'you do not have to provide one.');\n      }\n    }\n  }\n  if (muiFormControl && !variant) {\n    variant = muiFormControl.variant;\n  }\n  const ownerState = _extends({}, props, {\n    hiddenLabel: muiFormControl.hiddenLabel,\n    size: muiFormControl.size,\n    disablePointerEvents,\n    position,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormControlContext.Provider, {\n    value: null,\n    children: /*#__PURE__*/_jsx(InputAdornmentRoot, _extends({\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ref: ref\n    }, other, {\n      children: typeof children === 'string' && !disableTypography ? /*#__PURE__*/_jsx(Typography, {\n        color: \"text.secondary\",\n        children: children\n      }) : /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [position === 'start' ? (/* notranslate needed while Google Translate will not fix zero-width space issue */_span || (_span = /*#__PURE__*/_jsx(\"span\", {\n          className: \"notranslate\",\n          children: \"\\u200B\"\n        }))) : null, children]\n      })\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? InputAdornment.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or string.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Disable pointer events on the root.\n   * This allows for the content of the adornment to focus the `input` on click.\n   * @default false\n   */\n  disablePointerEvents: PropTypes.bool,\n  /**\n   * If children is a string then disable wrapping in a Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * The position this adornment should appear relative to the `Input`.\n   */\n  position: PropTypes.oneOf(['end', 'start']).isRequired,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * Note: If you are using the `TextField` component or the `FormControl` component\n   * you do not have to set this manually.\n   */\n  variant: PropTypes.oneOf(['filled', 'outlined', 'standard'])\n} : void 0;\nexport default InputAdornment;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}