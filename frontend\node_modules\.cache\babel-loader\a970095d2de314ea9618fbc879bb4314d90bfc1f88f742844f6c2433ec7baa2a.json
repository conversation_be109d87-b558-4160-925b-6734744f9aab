{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport addOneClass from 'dom-helpers/addClass';\nimport removeOneClass from 'dom-helpers/removeClass';\nimport React from 'react';\nimport Transition from './Transition';\nimport { classNamesShape } from './utils/PropTypes';\nimport { forceReflow } from './utils/reflow';\nvar _addClass = function addClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return addOneClass(node, c);\n  });\n};\nvar removeClass = function removeClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return removeOneClass(node, c);\n  });\n};\n/**\n * A transition component inspired by the excellent\n * [ng-animate](https://docs.angularjs.org/api/ngAnimate) library, you should\n * use it if you're using CSS transitions or animations. It's built upon the\n * [`Transition`](https://reactcommunity.org/react-transition-group/transition)\n * component, so it inherits all of its props.\n *\n * `CSSTransition` applies a pair of class names during the `appear`, `enter`,\n * and `exit` states of the transition. The first class is applied and then a\n * second `*-active` class in order to activate the CSS transition. After the\n * transition, matching `*-done` class names are applied to persist the\n * transition state.\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <CSSTransition in={inProp} timeout={200} classNames=\"my-node\">\n *         <div>\n *           {\"I'll receive my-node-* classes\"}\n *         </div>\n *       </CSSTransition>\n *       <button type=\"button\" onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the `in` prop is set to `true`, the child component will first receive\n * the class `example-enter`, then the `example-enter-active` will be added in\n * the next tick. `CSSTransition` [forces a\n * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)\n * between before adding the `example-enter-active`. This is an important trick\n * because it allows us to transition between `example-enter` and\n * `example-enter-active` even though they were added immediately one after\n * another. Most notably, this is what makes it possible for us to animate\n * _appearance_.\n *\n * ```css\n * .my-node-enter {\n *   opacity: 0;\n * }\n * .my-node-enter-active {\n *   opacity: 1;\n *   transition: opacity 200ms;\n * }\n * .my-node-exit {\n *   opacity: 1;\n * }\n * .my-node-exit-active {\n *   opacity: 0;\n *   transition: opacity 200ms;\n * }\n * ```\n *\n * `*-active` classes represent which styles you want to animate **to**, so it's\n * important to add `transition` declaration only to them, otherwise transitions\n * might not behave as intended! This might not be obvious when the transitions\n * are symmetrical, i.e. when `*-enter-active` is the same as `*-exit`, like in\n * the example above (minus `transition`), but it becomes apparent in more\n * complex transitions.\n *\n * **Note**: If you're using the\n * [`appear`](http://reactcommunity.org/react-transition-group/transition#Transition-prop-appear)\n * prop, make sure to define styles for `.appear-*` classes as well.\n */\n\nvar CSSTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(CSSTransition, _React$Component);\n  function CSSTransition() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.appliedClasses = {\n      appear: {},\n      enter: {},\n      exit: {}\n    };\n    _this.onEnter = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument = _this.resolveArguments(maybeNode, maybeAppearing),\n        node = _this$resolveArgument[0],\n        appearing = _this$resolveArgument[1];\n      _this.removeClasses(node, 'exit');\n      _this.addClass(node, appearing ? 'appear' : 'enter', 'base');\n      if (_this.props.onEnter) {\n        _this.props.onEnter(maybeNode, maybeAppearing);\n      }\n    };\n    _this.onEntering = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument2 = _this.resolveArguments(maybeNode, maybeAppearing),\n        node = _this$resolveArgument2[0],\n        appearing = _this$resolveArgument2[1];\n      var type = appearing ? 'appear' : 'enter';\n      _this.addClass(node, type, 'active');\n      if (_this.props.onEntering) {\n        _this.props.onEntering(maybeNode, maybeAppearing);\n      }\n    };\n    _this.onEntered = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument3 = _this.resolveArguments(maybeNode, maybeAppearing),\n        node = _this$resolveArgument3[0],\n        appearing = _this$resolveArgument3[1];\n      var type = appearing ? 'appear' : 'enter';\n      _this.removeClasses(node, type);\n      _this.addClass(node, type, 'done');\n      if (_this.props.onEntered) {\n        _this.props.onEntered(maybeNode, maybeAppearing);\n      }\n    };\n    _this.onExit = function (maybeNode) {\n      var _this$resolveArgument4 = _this.resolveArguments(maybeNode),\n        node = _this$resolveArgument4[0];\n      _this.removeClasses(node, 'appear');\n      _this.removeClasses(node, 'enter');\n      _this.addClass(node, 'exit', 'base');\n      if (_this.props.onExit) {\n        _this.props.onExit(maybeNode);\n      }\n    };\n    _this.onExiting = function (maybeNode) {\n      var _this$resolveArgument5 = _this.resolveArguments(maybeNode),\n        node = _this$resolveArgument5[0];\n      _this.addClass(node, 'exit', 'active');\n      if (_this.props.onExiting) {\n        _this.props.onExiting(maybeNode);\n      }\n    };\n    _this.onExited = function (maybeNode) {\n      var _this$resolveArgument6 = _this.resolveArguments(maybeNode),\n        node = _this$resolveArgument6[0];\n      _this.removeClasses(node, 'exit');\n      _this.addClass(node, 'exit', 'done');\n      if (_this.props.onExited) {\n        _this.props.onExited(maybeNode);\n      }\n    };\n    _this.resolveArguments = function (maybeNode, maybeAppearing) {\n      return _this.props.nodeRef ? [_this.props.nodeRef.current, maybeNode] // here `maybeNode` is actually `appearing`\n      : [maybeNode, maybeAppearing];\n    };\n    _this.getClassNames = function (type) {\n      var classNames = _this.props.classNames;\n      var isStringClassNames = typeof classNames === 'string';\n      var prefix = isStringClassNames && classNames ? classNames + \"-\" : '';\n      var baseClassName = isStringClassNames ? \"\" + prefix + type : classNames[type];\n      var activeClassName = isStringClassNames ? baseClassName + \"-active\" : classNames[type + \"Active\"];\n      var doneClassName = isStringClassNames ? baseClassName + \"-done\" : classNames[type + \"Done\"];\n      return {\n        baseClassName: baseClassName,\n        activeClassName: activeClassName,\n        doneClassName: doneClassName\n      };\n    };\n    return _this;\n  }\n  var _proto = CSSTransition.prototype;\n  _proto.addClass = function addClass(node, type, phase) {\n    var className = this.getClassNames(type)[phase + \"ClassName\"];\n    var _this$getClassNames = this.getClassNames('enter'),\n      doneClassName = _this$getClassNames.doneClassName;\n    if (type === 'appear' && phase === 'done' && doneClassName) {\n      className += \" \" + doneClassName;\n    } // This is to force a repaint,\n    // which is necessary in order to transition styles when adding a class name.\n\n    if (phase === 'active') {\n      if (node) forceReflow(node);\n    }\n    if (className) {\n      this.appliedClasses[type][phase] = className;\n      _addClass(node, className);\n    }\n  };\n  _proto.removeClasses = function removeClasses(node, type) {\n    var _this$appliedClasses$ = this.appliedClasses[type],\n      baseClassName = _this$appliedClasses$.base,\n      activeClassName = _this$appliedClasses$.active,\n      doneClassName = _this$appliedClasses$.done;\n    this.appliedClasses[type] = {};\n    if (baseClassName) {\n      removeClass(node, baseClassName);\n    }\n    if (activeClassName) {\n      removeClass(node, activeClassName);\n    }\n    if (doneClassName) {\n      removeClass(node, doneClassName);\n    }\n  };\n  _proto.render = function render() {\n    var _this$props = this.props,\n      _ = _this$props.classNames,\n      props = _objectWithoutPropertiesLoose(_this$props, [\"classNames\"]);\n    return /*#__PURE__*/React.createElement(Transition, _extends({}, props, {\n      onEnter: this.onEnter,\n      onEntered: this.onEntered,\n      onEntering: this.onEntering,\n      onExit: this.onExit,\n      onExiting: this.onExiting,\n      onExited: this.onExited\n    }));\n  };\n  return CSSTransition;\n}(React.Component);\nCSSTransition.defaultProps = {\n  classNames: ''\n};\nCSSTransition.propTypes = process.env.NODE_ENV !== \"production\" ? _extends({}, Transition.propTypes, {\n  /**\n   * The animation classNames applied to the component as it appears, enters,\n   * exits or has finished the transition. A single name can be provided, which\n   * will be suffixed for each stage, e.g. `classNames=\"fade\"` applies:\n   *\n   * - `fade-appear`, `fade-appear-active`, `fade-appear-done`\n   * - `fade-enter`, `fade-enter-active`, `fade-enter-done`\n   * - `fade-exit`, `fade-exit-active`, `fade-exit-done`\n   *\n   * A few details to note about how these classes are applied:\n   *\n   * 1. They are _joined_ with the ones that are already defined on the child\n   *    component, so if you want to add some base styles, you can use\n   *    `className` without worrying that it will be overridden.\n   *\n   * 2. If the transition component mounts with `in={false}`, no classes are\n   *    applied yet. You might be expecting `*-exit-done`, but if you think\n   *    about it, a component cannot finish exiting if it hasn't entered yet.\n   *\n   * 2. `fade-appear-done` and `fade-enter-done` will _both_ be applied. This\n   *    allows you to define different behavior for when appearing is done and\n   *    when regular entering is done, using selectors like\n   *    `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply\n   *    an epic entrance animation when element first appears in the DOM using\n   *    [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can\n   *    simply use `fade-enter-done` for defining both cases.\n   *\n   * Each individual classNames can also be specified independently like:\n   *\n   * ```js\n   * classNames={{\n   *  appear: 'my-appear',\n   *  appearActive: 'my-active-appear',\n   *  appearDone: 'my-done-appear',\n   *  enter: 'my-enter',\n   *  enterActive: 'my-active-enter',\n   *  enterDone: 'my-done-enter',\n   *  exit: 'my-exit',\n   *  exitActive: 'my-active-exit',\n   *  exitDone: 'my-done-exit',\n   * }}\n   * ```\n   *\n   * If you want to set these classes using CSS Modules:\n   *\n   * ```js\n   * import styles from './styles.css';\n   * ```\n   *\n   * you might want to use camelCase in your CSS file, that way could simply\n   * spread them instead of listing them one by one:\n   *\n   * ```js\n   * classNames={{ ...styles }}\n   * ```\n   *\n   * @type {string | {\n   *  appear?: string,\n   *  appearActive?: string,\n   *  appearDone?: string,\n   *  enter?: string,\n   *  enterActive?: string,\n   *  enterDone?: string,\n   *  exit?: string,\n   *  exitActive?: string,\n   *  exitDone?: string,\n   * }}\n   */\n  classNames: classNamesShape,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEnter: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter-active' or\n   * 'appear-active' class is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or\n   * 'appear' classes are **removed** and the `done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntered: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExit: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit-active' is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExiting: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' classes\n   * are **removed** and the `exit-done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExited: PropTypes.func\n}) : {};\nexport default CSSTransition;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}