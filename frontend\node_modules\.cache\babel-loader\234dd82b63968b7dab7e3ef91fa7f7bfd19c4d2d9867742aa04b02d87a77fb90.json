{"ast": null, "code": "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\nfunction effect(_ref) {\n  var state = _ref.state,\n    instance = _ref.instance,\n    options = _ref.options;\n  var _options$scroll = options.scroll,\n    scroll = _options$scroll === void 0 ? true : _options$scroll,\n    _options$resize = options.resize,\n    resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}