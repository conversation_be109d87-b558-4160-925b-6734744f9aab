{"ast": null, "code": "export const reflow = node => node.scrollTop;\nexport function getTransitionProps(props, options) {\n  var _style$transitionDura, _style$transitionTimi;\n  const {\n    timeout,\n    easing,\n    style = {}\n  } = props;\n  return {\n    duration: (_style$transitionDura = style.transitionDuration) != null ? _style$transitionDura : typeof timeout === 'number' ? timeout : timeout[options.mode] || 0,\n    easing: (_style$transitionTimi = style.transitionTimingFunction) != null ? _style$transitionTimi : typeof easing === 'object' ? easing[options.mode] : easing,\n    delay: style.transitionDelay\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}