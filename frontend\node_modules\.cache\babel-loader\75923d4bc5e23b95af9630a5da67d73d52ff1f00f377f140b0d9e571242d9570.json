{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"actionIcon\", \"actionPosition\", \"className\", \"subtitle\", \"title\", \"position\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport { getImageListItemBarUtilityClass } from './imageListItemBarClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    position,\n    actionIcon,\n    actionPosition\n  } = ownerState;\n  const slots = {\n    root: ['root', `position${capitalize(position)}`],\n    titleWrap: ['titleWrap', `titleWrap${capitalize(position)}`, actionIcon && `titleWrapActionPos${capitalize(actionPosition)}`],\n    title: ['title'],\n    subtitle: ['subtitle'],\n    actionIcon: ['actionIcon', `actionIconActionPos${capitalize(actionPosition)}`]\n  };\n  return composeClasses(slots, getImageListItemBarUtilityClass, classes);\n};\nconst ImageListItemBarRoot = styled('div', {\n  name: 'MuiImageListItemBar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`position${capitalize(ownerState.position)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({\n    position: 'absolute',\n    left: 0,\n    right: 0,\n    background: 'rgba(0, 0, 0, 0.5)',\n    display: 'flex',\n    alignItems: 'center',\n    fontFamily: theme.typography.fontFamily\n  }, ownerState.position === 'bottom' && {\n    bottom: 0\n  }, ownerState.position === 'top' && {\n    top: 0\n  }, ownerState.position === 'below' && {\n    position: 'relative',\n    background: 'transparent',\n    alignItems: 'normal'\n  });\n});\nconst ImageListItemBarTitleWrap = styled('div', {\n  name: 'MuiImageListItemBar',\n  slot: 'TitleWrap',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.titleWrap, styles[`titleWrap${capitalize(ownerState.position)}`], ownerState.actionIcon && styles[`titleWrapActionPos${capitalize(ownerState.actionPosition)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({\n    flexGrow: 1,\n    padding: '12px 16px',\n    color: (theme.vars || theme).palette.common.white,\n    overflow: 'hidden'\n  }, ownerState.position === 'below' && {\n    padding: '6px 0 12px',\n    color: 'inherit'\n  }, ownerState.actionIcon && ownerState.actionPosition === 'left' && {\n    paddingLeft: 0\n  }, ownerState.actionIcon && ownerState.actionPosition === 'right' && {\n    paddingRight: 0\n  });\n});\nconst ImageListItemBarTitle = styled('div', {\n  name: 'MuiImageListItemBar',\n  slot: 'Title',\n  overridesResolver: (props, styles) => styles.title\n})(({\n  theme\n}) => {\n  return {\n    fontSize: theme.typography.pxToRem(16),\n    lineHeight: '24px',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    whiteSpace: 'nowrap'\n  };\n});\nconst ImageListItemBarSubtitle = styled('div', {\n  name: 'MuiImageListItemBar',\n  slot: 'Subtitle',\n  overridesResolver: (props, styles) => styles.subtitle\n})(({\n  theme\n}) => {\n  return {\n    fontSize: theme.typography.pxToRem(12),\n    lineHeight: 1,\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    whiteSpace: 'nowrap'\n  };\n});\nconst ImageListItemBarActionIcon = styled('div', {\n  name: 'MuiImageListItemBar',\n  slot: 'ActionIcon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.actionIcon, styles[`actionIconActionPos${capitalize(ownerState.actionPosition)}`]];\n  }\n})(({\n  ownerState\n}) => {\n  return _extends({}, ownerState.actionPosition === 'left' && {\n    order: -1\n  });\n});\nconst ImageListItemBar = /*#__PURE__*/React.forwardRef(function ImageListItemBar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiImageListItemBar'\n  });\n  const {\n      actionIcon,\n      actionPosition = 'right',\n      className,\n      subtitle,\n      title,\n      position = 'bottom'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    position,\n    actionPosition\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(ImageListItemBarRoot, _extends({\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [/*#__PURE__*/_jsxs(ImageListItemBarTitleWrap, {\n      ownerState: ownerState,\n      className: classes.titleWrap,\n      children: [/*#__PURE__*/_jsx(ImageListItemBarTitle, {\n        className: classes.title,\n        children: title\n      }), subtitle ? /*#__PURE__*/_jsx(ImageListItemBarSubtitle, {\n        className: classes.subtitle,\n        children: subtitle\n      }) : null]\n    }), actionIcon ? /*#__PURE__*/_jsx(ImageListItemBarActionIcon, {\n      ownerState: ownerState,\n      className: classes.actionIcon,\n      children: actionIcon\n    }) : null]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageListItemBar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * An IconButton element to be used as secondary action target\n   * (primary action target is the item itself).\n   */\n  actionIcon: PropTypes.node,\n  /**\n   * Position of secondary action IconButton.\n   * @default 'right'\n   */\n  actionPosition: PropTypes.oneOf(['left', 'right']),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Position of the title bar.\n   * @default 'bottom'\n   */\n  position: PropTypes.oneOf(['below', 'bottom', 'top']),\n  /**\n   * String or element serving as subtitle (support text).\n   */\n  subtitle: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Title to be displayed.\n   */\n  title: PropTypes.node\n} : void 0;\nexport default ImageListItemBar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}