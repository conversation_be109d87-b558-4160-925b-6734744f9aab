{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"anchor\", \"classes\", \"className\", \"width\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport capitalize from '../utils/capitalize';\nimport { isHorizontal } from '../Drawer/Drawer';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SwipeAreaRoot = styled('div', {\n  name: 'MuiSwipeArea',\n  shouldForwardProp: rootShouldForwardProp\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  top: 0,\n  left: 0,\n  bottom: 0,\n  zIndex: theme.zIndex.drawer - 1\n}, ownerState.anchor === 'left' && {\n  right: 'auto'\n}, ownerState.anchor === 'right' && {\n  left: 'auto',\n  right: 0\n}, ownerState.anchor === 'top' && {\n  bottom: 'auto',\n  right: 0\n}, ownerState.anchor === 'bottom' && {\n  top: 'auto',\n  bottom: 0,\n  right: 0\n}));\n\n/**\n * @ignore - internal component.\n */\nconst SwipeArea = /*#__PURE__*/React.forwardRef(function SwipeArea(props, ref) {\n  const {\n      anchor,\n      classes = {},\n      className,\n      width,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(SwipeAreaRoot, _extends({\n    className: clsx('PrivateSwipeArea-root', classes.root, classes[`anchor${capitalize(anchor)}`], className),\n    ref: ref,\n    style: _extends({\n      [isHorizontal(anchor) ? 'width' : 'height']: width\n    }, style),\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeArea.propTypes = {\n  /**\n   * Side on which to attach the discovery area.\n   */\n  anchor: PropTypes.oneOf(['left', 'top', 'right', 'bottom']).isRequired,\n  /**\n   * @ignore\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` where the\n   * drawer can be swiped open from.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport default SwipeArea;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}