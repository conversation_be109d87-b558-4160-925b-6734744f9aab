import React, { useState, useEffect } from 'react';
import { Grid, Paper, Typography, Box, Card, CardContent } from '@mui/material';
import { TrendingUp, TrendingDown, AccountBalance, Savings } from '@mui/icons-material';
import BalanceChart from './components/BalanceChart';
import ExpenseChart from './components/ExpenseChart';
import RecentTransactions from './components/RecentTransactions';
import { formatCurrency } from '../../utils/formatters';
import { dashboardService } from '../../services/dashboardService';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    balance: 0,
    monthlyIncome: 0,
    monthlyExpenses: 0,
    savings: 0,
    recentTransactions: [],
    balanceHistory: [],
    expensesByCategory: []
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const data = await dashboardService.getDashboardData();
      setDashboardData(data);
    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
    }
  };

  const StatCard = ({ title, value, icon, color, trend }) => (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h5" component="h2">
              {formatCurrency(value)}
            </Typography>
            {trend && (
              <Typography variant="body2" color={trend > 0 ? 'success.main' : 'error.main'}>
                {trend > 0 ? '+' : ''}{trend.toFixed(1)}% vs mês anterior
              </Typography>
            )}
          </Box>
          <Box sx={{ color: color }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard Financeiro
      </Typography>
      
      <Grid container spacing={3}>
        {/* Cards de Resumo */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Saldo Atual"
            value={dashboardData.balance}
            icon={<AccountBalance fontSize="large" />}
            color="primary.main"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Receitas do Mês"
            value={dashboardData.monthlyIncome}
            icon={<TrendingUp fontSize="large" />}
            color="success.main"
            trend={12.5}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Despesas do Mês"
            value={dashboardData.monthlyExpenses}
            icon={<TrendingDown fontSize="large" />}
            color="error.main"
            trend={-8.2}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Economia"
            value={dashboardData.savings}
            icon={<Savings fontSize="large" />}
            color="info.main"
            trend={15.3}
          />
        </Grid>

        {/* Gráfico de Evolução do Saldo */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Evolução do Saldo
            </Typography>
            <BalanceChart data={dashboardData.balanceHistory} />
          </Paper>
        </Grid>

        {/* Gráfico de Despesas por Categoria */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Despesas por Categoria
            </Typography>
            <ExpenseChart data={dashboardData.expensesByCategory} />
          </Paper>
        </Grid>

        {/* Transações Recentes */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Transações Recentes
            </Typography>
            <RecentTransactions transactions={dashboardData.recentTransactions} />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;