const db = require('../config/database');

const dashboardController = {
  async getDashboardData(req, res) {
    try {
      const userId = req.user.id;
      
      // Saldo atual
      const balanceResult = await db.query(
        'SELECT COALESCE(SUM(CASE WHEN type = \'income\' THEN amount ELSE -amount END), 0) as balance FROM transactions WHERE user_id = $1',
        [userId]
      );
      
      // Receitas e despesas do mês atual
      const currentMonth = new Date().toISOString().slice(0, 7);
      const monthlyResult = await db.query(`
        SELECT 
          COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as monthly_income,
          COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as monthly_expenses
        FROM transactions 
        WHERE user_id = $1 AND DATE_TRUNC('month', date) = $2::date
      `, [userId, currentMonth + '-01']);
      
      // Transações recentes
      const recentTransactions = await db.query(`
        SELECT t.*, c.name as category_name, c.color as category_color
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        WHERE t.user_id = $1
        ORDER BY t.date DESC, t.created_at DESC
        LIMIT 10
      `, [userId]);
      
      const balance = parseFloat(balanceResult.rows[0].balance);
      const monthlyIncome = parseFloat(monthlyResult.rows[0].monthly_income);
      const monthlyExpenses = parseFloat(monthlyResult.rows[0].monthly_expenses);
      
      res.json({
        balance,
        monthlyIncome,
        monthlyExpenses,
        savings: monthlyIncome - monthlyExpenses,
        recentTransactions: recentTransactions.rows,
        balanceHistory: [], // Implementar histórico
        expensesByCategory: [] // Implementar categorias
      });
    } catch (error) {
      console.error('Erro ao buscar dados do dashboard:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
};

module.exports = dashboardController;