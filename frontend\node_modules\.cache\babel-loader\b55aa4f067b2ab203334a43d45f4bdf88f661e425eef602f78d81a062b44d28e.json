{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCircularProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiCircularProgress', slot);\n}\nconst circularProgressClasses = generateUtilityClasses('MuiCircularProgress', ['root', 'determinate', 'indeterminate', 'colorPrimary', 'colorSecondary', 'svg', 'circle', 'circleDeterminate', 'circleIndeterminate', 'circleDisableShrink']);\nexport default circularProgressClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}