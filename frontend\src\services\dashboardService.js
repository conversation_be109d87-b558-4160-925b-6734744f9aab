import axios from 'axios';

export const dashboardService = {
  async getDashboardData() {
    const response = await axios.get('/api/dashboard');
    return response.data;
  },

  async getBalanceHistory(period = '6months') {
    const response = await axios.get(`/api/dashboard/balance-history?period=${period}`);
    return response.data;
  },

  async getExpensesByCategory(period = 'current-month') {
    const response = await axios.get(`/api/dashboard/expenses-by-category?period=${period}`);
    return response.data;
  }
};