{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RtlContext = /*#__PURE__*/React.createContext();\nfunction RtlProvider(_ref) {\n  let {\n      value\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return /*#__PURE__*/_jsx(RtlContext.Provider, _extends({\n    value: value != null ? value : true\n  }, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? RtlProvider.propTypes = {\n  children: PropTypes.node,\n  value: PropTypes.bool\n} : void 0;\nexport const useRtl = () => {\n  const value = React.useContext(RtlContext);\n  return value != null ? value : false;\n};\nexport default RtlProvider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}