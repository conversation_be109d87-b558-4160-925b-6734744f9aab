{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"align\", \"className\", \"component\", \"gutterBottom\", \"noWrap\", \"paragraph\", \"variant\", \"variantMapping\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { extendSxProp } from '@mui/system/styleFunctionSx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport { getTypographyUtilityClass } from './typographyClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, ownerState.align !== 'inherit' && `align${capitalize(align)}`, gutterBottom && 'gutterBottom', noWrap && 'noWrap', paragraph && 'paragraph']\n  };\n  return composeClasses(slots, getTypographyUtilityClass, classes);\n};\nexport const TypographyRoot = styled('span', {\n  name: 'MuiTypography',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.variant && styles[ownerState.variant], ownerState.align !== 'inherit' && styles[`align${capitalize(ownerState.align)}`], ownerState.noWrap && styles.noWrap, ownerState.gutterBottom && styles.gutterBottom, ownerState.paragraph && styles.paragraph];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  margin: 0\n}, ownerState.variant === 'inherit' && {\n  // Some elements, like <button> on Chrome have default font that doesn't inherit, reset this.\n  font: 'inherit'\n}, ownerState.variant !== 'inherit' && theme.typography[ownerState.variant], ownerState.align !== 'inherit' && {\n  textAlign: ownerState.align\n}, ownerState.noWrap && {\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  whiteSpace: 'nowrap'\n}, ownerState.gutterBottom && {\n  marginBottom: '0.35em'\n}, ownerState.paragraph && {\n  marginBottom: 16\n}));\nconst defaultVariantMapping = {\n  h1: 'h1',\n  h2: 'h2',\n  h3: 'h3',\n  h4: 'h4',\n  h5: 'h5',\n  h6: 'h6',\n  subtitle1: 'h6',\n  subtitle2: 'h6',\n  body1: 'p',\n  body2: 'p',\n  inherit: 'p'\n};\n\n// TODO v6: deprecate these color values in v5.x and remove the transformation in v6\nconst colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst Typography = /*#__PURE__*/React.forwardRef(function Typography(inProps, ref) {\n  const themeProps = useDefaultProps({\n    props: inProps,\n    name: 'MuiTypography'\n  });\n  const color = transformDeprecatedColors(themeProps.color);\n  const props = extendSxProp(_extends({}, themeProps, {\n    color\n  }));\n  const {\n      align = 'inherit',\n      className,\n      component,\n      gutterBottom = false,\n      noWrap = false,\n      paragraph = false,\n      variant = 'body1',\n      variantMapping = defaultVariantMapping\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    align,\n    color,\n    className,\n    component,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    variantMapping\n  });\n  const Component = component || (paragraph ? 'p' : variantMapping[variant] || defaultVariantMapping[variant]) || 'span';\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TypographyRoot, _extends({\n    as: Component,\n    ref: ref,\n    ownerState: ownerState,\n    className: clsx(classes.root, className)\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Typography.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Set the text-align on the component.\n   * @default 'inherit'\n   */\n  align: PropTypes.oneOf(['center', 'inherit', 'justify', 'left', 'right']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the text will have a bottom margin.\n   * @default false\n   */\n  gutterBottom: PropTypes.bool,\n  /**\n   * If `true`, the text will not wrap, but instead will truncate with a text overflow ellipsis.\n   *\n   * Note that text overflow can only happen with block or inline-block level elements\n   * (the element needs to have a width in order to overflow).\n   * @default false\n   */\n  noWrap: PropTypes.bool,\n  /**\n   * If `true`, the element will be a paragraph element.\n   * @default false\n   */\n  paragraph: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Applies the theme typography styles.\n   * @default 'body1'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string]),\n  /**\n   * The component maps the variant prop to a range of different HTML element types.\n   * For instance, subtitle1 to `<h6>`.\n   * If you wish to change that mapping, you can provide your own.\n   * Alternatively, you can use the `component` prop.\n   * @default {\n   *   h1: 'h1',\n   *   h2: 'h2',\n   *   h3: 'h3',\n   *   h4: 'h4',\n   *   h5: 'h5',\n   *   h6: 'h6',\n   *   subtitle1: 'h6',\n   *   subtitle2: 'h6',\n   *   body1: 'p',\n   *   body2: 'p',\n   *   inherit: 'p',\n   * }\n   */\n  variantMapping: PropTypes /* @typescript-to-proptypes-ignore */.object\n} : void 0;\nexport default Typography;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}