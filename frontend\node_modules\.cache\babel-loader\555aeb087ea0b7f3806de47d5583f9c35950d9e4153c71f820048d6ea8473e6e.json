{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAccordionActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionActions', slot);\n}\nconst accordionActionsClasses = generateUtilityClasses('MuiAccordionActions', ['root', 'spacing']);\nexport default accordionActionsClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}