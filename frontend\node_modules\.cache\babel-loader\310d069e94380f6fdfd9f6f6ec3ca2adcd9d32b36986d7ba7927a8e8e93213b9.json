{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"addEndListener\", \"appear\", \"children\", \"easing\", \"in\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\", \"style\", \"timeout\", \"TransitionComponent\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { Transition } from 'react-transition-group';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport useTheme from '../styles/useTheme';\nimport { reflow, getTransitionProps } from '../transitions/utils';\nimport useForkRef from '../utils/useForkRef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  entering: {\n    transform: 'none'\n  },\n  entered: {\n    transform: 'none'\n  }\n};\n\n/**\n * The Zoom transition can be used for the floating variant of the\n * [Button](/material-ui/react-button/#floating-action-buttons) component.\n * It uses [react-transition-group](https://github.com/reactjs/react-transition-group) internally.\n */\nconst Zoom = /*#__PURE__*/React.forwardRef(function Zoom(props, ref) {\n  const theme = useTheme();\n  const defaultTimeout = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      addEndListener,\n      appear = true,\n      children,\n      easing,\n      in: inProp,\n      onEnter,\n      onEntered,\n      onEntering,\n      onExit,\n      onExited,\n      onExiting,\n      style,\n      timeout = defaultTimeout,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Transition\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const nodeRef = React.useRef(null);\n  const handleRef = useForkRef(nodeRef, getReactElementRef(children), ref);\n  const normalizedTransitionCallback = callback => maybeIsAppearing => {\n    if (callback) {\n      const node = nodeRef.current;\n\n      // onEnterXxx and onExitXxx callbacks have a different arguments.length value.\n      if (maybeIsAppearing === undefined) {\n        callback(node);\n      } else {\n        callback(node, maybeIsAppearing);\n      }\n    }\n  };\n  const handleEntering = normalizedTransitionCallback(onEntering);\n  const handleEnter = normalizedTransitionCallback((node, isAppearing) => {\n    reflow(node); // So the animation always start from the start.\n\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'enter'\n    });\n    node.style.webkitTransition = theme.transitions.create('transform', transitionProps);\n    node.style.transition = theme.transitions.create('transform', transitionProps);\n    if (onEnter) {\n      onEnter(node, isAppearing);\n    }\n  });\n  const handleEntered = normalizedTransitionCallback(onEntered);\n  const handleExiting = normalizedTransitionCallback(onExiting);\n  const handleExit = normalizedTransitionCallback(node => {\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'exit'\n    });\n    node.style.webkitTransition = theme.transitions.create('transform', transitionProps);\n    node.style.transition = theme.transitions.create('transform', transitionProps);\n    if (onExit) {\n      onExit(node);\n    }\n  });\n  const handleExited = normalizedTransitionCallback(onExited);\n  const handleAddEndListener = next => {\n    if (addEndListener) {\n      // Old call signature before `react-transition-group` implemented `nodeRef`\n      addEndListener(nodeRef.current, next);\n    }\n  };\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    appear: appear,\n    in: inProp,\n    nodeRef: nodeRef,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    timeout: timeout\n  }, other, {\n    children: (state, childProps) => {\n      return /*#__PURE__*/React.cloneElement(children, _extends({\n        style: _extends({\n          transform: 'scale(0)',\n          visibility: state === 'exited' && !inProp ? 'hidden' : undefined\n        }, styles[state], style, children.props.style),\n        ref: handleRef\n      }, childProps));\n    }\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Zoom.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Add a custom transition end trigger. Called with the transitioning DOM\n   * node and a done callback. Allows for more fine grained transition end\n   * logic. Note: Timeouts are still used as a fallback if provided.\n   */\n  addEndListener: PropTypes.func,\n  /**\n   * Perform the enter transition when it first mounts if `in` is also `true`.\n   * Set this to `false` to disable this behavior.\n   * @default true\n   */\n  appear: PropTypes.bool,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * The transition timing function.\n   * You may specify a single easing or a object containing enter and exit values.\n   */\n  easing: PropTypes.oneOfType([PropTypes.shape({\n    enter: PropTypes.string,\n    exit: PropTypes.string\n  }), PropTypes.string]),\n  /**\n   * If `true`, the component will transition in.\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntered: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntering: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExit: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExited: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExiting: PropTypes.func,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  timeout: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Zoom;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}