{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaginationUtilityClass(slot) {\n  return generateUtilityClass('MuiPagination', slot);\n}\nconst paginationClasses = generateUtilityClasses('MuiPagination', ['root', 'ul', 'outlined', 'text']);\nexport default paginationClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}