{"name": "finance-manager-frontend", "version": "1.0.0", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@mui/material": "^5.11.0", "@mui/icons-material": "^5.11.0", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "chart.js": "^4.2.0", "react-chartjs-2": "^5.2.0", "axios": "^1.3.0", "date-fns": "^2.29.0", "react-hook-form": "^7.43.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test"}}