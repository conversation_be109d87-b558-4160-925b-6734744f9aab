[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\Dashboard.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Auth\\Login.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Reports\\Reports.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Goals\\Goals.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Transactions\\Transactions.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Auth\\Register.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\components\\Layout\\Layout.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\components\\BalanceChart.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\components\\ExpenseChart.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\transactionService.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\categoryService.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\components\\RecentTransactions.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\dashboardService.js": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\authService.js": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\utils\\formatters.js": "18"}, {"size": 975, "mtime": 1753897428575, "results": "19", "hashOfConfig": "20"}, {"size": 1440, "mtime": 1753897692335, "results": "21", "hashOfConfig": "20"}, {"size": 1573, "mtime": 1753897117103, "results": "22", "hashOfConfig": "20"}, {"size": 4239, "mtime": 1753897124338, "results": "23", "hashOfConfig": "20"}, {"size": 3441, "mtime": 1753897499327, "results": "24", "hashOfConfig": "20"}, {"size": 7427, "mtime": 1753897565823, "results": "25", "hashOfConfig": "20"}, {"size": 10303, "mtime": 1753897606739, "results": "26", "hashOfConfig": "20"}, {"size": 9310, "mtime": 1753897533523, "results": "27", "hashOfConfig": "20"}, {"size": 4626, "mtime": 1753897660578, "results": "28", "hashOfConfig": "20"}, {"size": 2083, "mtime": 1753897121365, "results": "29", "hashOfConfig": "20"}, {"size": 1063, "mtime": 1753897128534, "results": "30", "hashOfConfig": "20"}, {"size": 2083, "mtime": 1753897622450, "results": "31", "hashOfConfig": "20"}, {"size": 1198, "mtime": 1753897471994, "results": "32", "hashOfConfig": "20"}, {"size": 957, "mtime": 1753897482430, "results": "33", "hashOfConfig": "20"}, {"size": 3370, "mtime": 1753897640108, "results": "34", "hashOfConfig": "20"}, {"size": 532, "mtime": 1753897129429, "results": "35", "hashOfConfig": "20"}, {"size": 888, "mtime": 1753897461549, "results": "36", "hashOfConfig": "20"}, {"size": 1831, "mtime": 1753897452106, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tnehlq", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Reports\\Reports.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Goals\\Goals.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Transactions\\Transactions.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Auth\\Register.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\components\\Layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\components\\BalanceChart.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\components\\ExpenseChart.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\transactionService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\categoryService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\pages\\Dashboard\\components\\RecentTransactions.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\dashboardService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Planejamento Financeiro\\frontend\\src\\utils\\formatters.js", [], []]