{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAccordionDetailsUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionDetails', slot);\n}\nconst accordionDetailsClasses = generateUtilityClasses('MuiAccordionDetails', ['root']);\nexport default accordionDetailsClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}