{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListSubheaderUtilityClass(slot) {\n  return generateUtilityClass('MuiListSubheader', slot);\n}\nconst listSubheaderClasses = generateUtilityClasses('MuiListSubheader', ['root', 'colorPrimary', 'colorInherit', 'gutters', 'inset', 'sticky']);\nexport default listSubheaderClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}