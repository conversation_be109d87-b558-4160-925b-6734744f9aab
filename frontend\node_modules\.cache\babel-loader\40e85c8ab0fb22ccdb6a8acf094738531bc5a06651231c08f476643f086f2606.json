{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabUtilityClass(slot) {\n  return generateUtilityClass('MuiTab', slot);\n}\nconst tabClasses = generateUtilityClasses('MuiTab', ['root', 'labelIcon', 'textColorInherit', 'textColorPrimary', 'textColorSecondary', 'selected', 'disabled', 'fullWidth', 'wrapped', 'iconWrapper']);\nexport default tabClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}