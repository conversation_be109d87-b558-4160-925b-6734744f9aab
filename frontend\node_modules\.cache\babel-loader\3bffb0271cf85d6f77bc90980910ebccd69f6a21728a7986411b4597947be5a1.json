{"ast": null, "code": "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\nfunction toObject(val) {\n  if (val === null || val === undefined) {\n    throw new TypeError('Object.assign cannot be called with null or undefined');\n  }\n  return Object(val);\n}\nfunction shouldUseNative() {\n  try {\n    if (!Object.assign) {\n      return false;\n    }\n\n    // Detect buggy property enumeration order in older V8 versions.\n\n    // https://bugs.chromium.org/p/v8/issues/detail?id=4118\n    var test1 = new String('abc'); // eslint-disable-line no-new-wrappers\n    test1[5] = 'de';\n    if (Object.getOwnPropertyNames(test1)[0] === '5') {\n      return false;\n    }\n\n    // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n    var test2 = {};\n    for (var i = 0; i < 10; i++) {\n      test2['_' + String.fromCharCode(i)] = i;\n    }\n    var order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n      return test2[n];\n    });\n    if (order2.join('') !== '0123456789') {\n      return false;\n    }\n\n    // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n    var test3 = {};\n    'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n      test3[letter] = letter;\n    });\n    if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {\n      return false;\n    }\n    return true;\n  } catch (err) {\n    // We don't expect any of the above to throw, but better to be safe.\n    return false;\n  }\n}\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n  var from;\n  var to = toObject(target);\n  var symbols;\n  for (var s = 1; s < arguments.length; s++) {\n    from = Object(arguments[s]);\n    for (var key in from) {\n      if (hasOwnProperty.call(from, key)) {\n        to[key] = from[key];\n      }\n    }\n    if (getOwnPropertySymbols) {\n      symbols = getOwnPropertySymbols(from);\n      for (var i = 0; i < symbols.length; i++) {\n        if (propIsEnumerable.call(from, symbols[i])) {\n          to[symbols[i]] = from[symbols[i]];\n        }\n      }\n    }\n  }\n  return to;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}