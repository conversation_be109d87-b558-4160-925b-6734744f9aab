{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"getTrigger\", \"target\"];\nimport * as React from 'react';\nfunction defaultTrigger(store, options) {\n  const {\n    disableHysteresis = false,\n    threshold = 100,\n    target\n  } = options;\n  const previous = store.current;\n  if (target) {\n    // Get vertical scroll\n    store.current = target.pageYOffset !== undefined ? target.pageYOffset : target.scrollTop;\n  }\n  if (!disableHysteresis && previous !== undefined) {\n    if (store.current < previous) {\n      return false;\n    }\n  }\n  return store.current > threshold;\n}\nconst defaultTarget = typeof window !== 'undefined' ? window : null;\nexport default function useScrollTrigger(options = {}) {\n  const {\n      getTrigger = defaultTrigger,\n      target = defaultTarget\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  const store = React.useRef();\n  const [trigger, setTrigger] = React.useState(() => getTrigger(store, other));\n  React.useEffect(() => {\n    const handleScroll = () => {\n      setTrigger(getTrigger(store, _extends({\n        target\n      }, other)));\n    };\n    handleScroll(); // Re-evaluate trigger when dependencies change\n    target.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    return () => {\n      target.removeEventListener('scroll', handleScroll, {\n        passive: true\n      });\n    };\n    // See Option 3. https://github.com/facebook/react/issues/14476#issuecomment-471199055\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [target, getTrigger, JSON.stringify(other)]);\n  return trigger;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}