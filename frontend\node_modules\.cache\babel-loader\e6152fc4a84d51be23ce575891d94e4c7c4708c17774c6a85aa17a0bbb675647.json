{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiFormGroup', slot);\n}\nconst formGroupClasses = generateUtilityClasses('MuiFormGroup', ['root', 'row', 'error']);\nexport default formGroupClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}