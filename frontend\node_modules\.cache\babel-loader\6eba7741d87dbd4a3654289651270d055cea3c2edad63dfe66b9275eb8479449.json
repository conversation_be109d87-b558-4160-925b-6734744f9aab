{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"active\", \"children\", \"className\", \"direction\", \"hideSortIcon\", \"IconComponent\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport ButtonBase from '../ButtonBase';\nimport ArrowDownwardIcon from '../internal/svg-icons/ArrowDownward';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport tableSortLabelClasses, { getTableSortLabelUtilityClass } from './tableSortLabelClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    direction,\n    active\n  } = ownerState;\n  const slots = {\n    root: ['root', active && 'active'],\n    icon: ['icon', `iconDirection${capitalize(direction)}`]\n  };\n  return composeClasses(slots, getTableSortLabelUtilityClass, classes);\n};\nconst TableSortLabelRoot = styled(ButtonBase, {\n  name: 'MuiTableSortLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.active && styles.active];\n  }\n})(({\n  theme\n}) => ({\n  cursor: 'pointer',\n  display: 'inline-flex',\n  justifyContent: 'flex-start',\n  flexDirection: 'inherit',\n  alignItems: 'center',\n  '&:focus': {\n    color: (theme.vars || theme).palette.text.secondary\n  },\n  '&:hover': {\n    color: (theme.vars || theme).palette.text.secondary,\n    [`& .${tableSortLabelClasses.icon}`]: {\n      opacity: 0.5\n    }\n  },\n  [`&.${tableSortLabelClasses.active}`]: {\n    color: (theme.vars || theme).palette.text.primary,\n    [`& .${tableSortLabelClasses.icon}`]: {\n      opacity: 1,\n      color: (theme.vars || theme).palette.text.secondary\n    }\n  }\n}));\nconst TableSortLabelIcon = styled('span', {\n  name: 'MuiTableSortLabel',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, styles[`iconDirection${capitalize(ownerState.direction)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  fontSize: 18,\n  marginRight: 4,\n  marginLeft: 4,\n  opacity: 0,\n  transition: theme.transitions.create(['opacity', 'transform'], {\n    duration: theme.transitions.duration.shorter\n  }),\n  userSelect: 'none'\n}, ownerState.direction === 'desc' && {\n  transform: 'rotate(0deg)'\n}, ownerState.direction === 'asc' && {\n  transform: 'rotate(180deg)'\n}));\n\n/**\n * A button based label for placing inside `TableCell` for column sorting.\n */\nconst TableSortLabel = /*#__PURE__*/React.forwardRef(function TableSortLabel(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableSortLabel'\n  });\n  const {\n      active = false,\n      children,\n      className,\n      direction = 'asc',\n      hideSortIcon = false,\n      IconComponent = ArrowDownwardIcon\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    active,\n    direction,\n    hideSortIcon,\n    IconComponent\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(TableSortLabelRoot, _extends({\n    className: clsx(classes.root, className),\n    component: \"span\",\n    disableRipple: true,\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: [children, hideSortIcon && !active ? null : /*#__PURE__*/_jsx(TableSortLabelIcon, {\n      as: IconComponent,\n      className: clsx(classes.icon),\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TableSortLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the label will have the active styling (should be true for the sorted column).\n   * @default false\n   */\n  active: PropTypes.bool,\n  /**\n   * Label contents, the arrow will be appended automatically.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The current sort direction.\n   * @default 'asc'\n   */\n  direction: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Hide sort icon when active is false.\n   * @default false\n   */\n  hideSortIcon: PropTypes.bool,\n  /**\n   * Sort icon to use.\n   * @default ArrowDownwardIcon\n   */\n  IconComponent: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableSortLabel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}